{"name": "selenium-bun-automation", "version": "1.0.0", "description": "Modern Selenium browser automation with Bun and TypeScript (2025)", "main": "index.ts", "type": "module", "scripts": {"start": "bun run index.ts", "dev": "bun --watch index.ts", "test": "bun test", "build": "bun build index.ts --outdir ./dist --target node", "clean": "rm -rf dist screenshots data node_modules", "setup": "bun install && bun run setup-drivers"}, "keywords": ["selenium", "webdriver", "automation", "bun", "typescript", "testing", "browser-automation"], "author": "Your Name", "license": "MIT", "dependencies": {"rimraf": "^6.0.1", "selenium-webdriver": "^4.34.0"}, "devDependencies": {"@types/selenium-webdriver": "^4.1.28", "typescript": "^5.3.0", "bun-types": "latest"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/selenium-bun-automation.git"}, "bugs": {"url": "https://github.com/yourusername/selenium-bun-automation/issues"}, "homepage": "https://github.com/yourusername/selenium-bun-automation#readme"}