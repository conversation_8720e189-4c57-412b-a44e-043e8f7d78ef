import { Builder, By, Key, until } from 'selenium-webdriver';
import { Options } from 'selenium-webdriver/chrome';

async function googleTest() {
  const options = new Options();
  options.addArguments('--disable-blink-features=AutomationControlled');
  
  const driver = await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .build();

  try {
    await driver.get('https://google.com');
    await driver.sleep(2000);
    
    const search = await driver.wait(until.elementLocated(By.name('q')), 10000);
    await search.sendKeys('selenium automation');
    await search.sendKeys(Key.RETURN);
    await driver.sleep(3000);
    
  } finally {
    await driver.quit();
  }
}

// TEMPLATE 2: SIMPLE FORM (RELIABLE)
async function formTest() {
  const driver = await new Builder().forBrowser('chrome').build();
//   await driver.sleep(10000);
  
  try {
    await driver.get('https://httpbin.org/forms/post');
    
    await driver.findElement(By.name('custname')).sendKeys('Test User');
    await driver.findElement(By.name('custemail')).sendKeys('<EMAIL>');
    await driver.findElement(By.name('topping')).click();
    await driver.sleep(1000);
    // await driver.findElement(By.css('input[type="submit"]')).click();
    await driver.sleep(2000);
    
  } finally {
    await driver.quit();
  }
}

// TEMPLATE 3: E-COMMERCE (FLIPKART/AMAZON)
async function ecommerceTest() {
  const options = new Options();
  options.addArguments('--disable-blink-features=AutomationControlled');
  
  const driver = await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .build();

  try {
    await driver.get('https://flipkart.com');
    await driver.sleep(3000);
    
    // Handle popup with try-catch
    try {
      await driver.findElement(By.css('button._2KpZ6l')).click();
    } catch {}
    
    // Search
    const search = await driver.wait(until.elementLocated(By.name('q')), 10000);
    await search.sendKeys('mobile phone');
    await search.sendKeys(Key.RETURN);
    await driver.sleep(5000);

    await driver.executeScript('window.scrollTo(0, 1000);');
    await driver.sleep(1000);
    await driver.executeScript('window.scrollTo(0, 2000);');
    await driver.sleep(1000);
    await driver.executeScript('window.scrollTo(0, 3000);');
    await driver.sleep(1000);
    await driver.executeScript('window.scrollTo(0, 4000);');
    await driver.sleep(1000);
    await driver.executeScript('window.scrollTo(0, 5000);');
    
    // Extract data with multiple selectors
    try {
      const products = await driver.findElements(By.css('.tUxRFH, .tUxRFH, .CGtC98'));
      if (products.length > 0) {
        const firstProduct = await products[0]?.getText();
        console.log('First product:', firstProduct);
      }
    } catch {
      console.log('Products not found - page structure might have changed');
    }
    
  } finally {
    await driver.quit();
  }
}

// TEMPLATE 4: CLICK & NAVIGATE
async function navigationTest() {
  const driver = await new Builder().forBrowser('chrome').build();
  
  try {
    await driver.get('https://example.com');
    
    // Click a link
    const link = await driver.findElement(By.tagName('a'));
    await link.click();
    await driver.sleep(2000);
    
    // Go back
    await driver.navigate().back();
    await driver.sleep(1000);
    
  } finally {
    await driver.quit();
  }
}

// SUPER MINIMAL FOR SPEED (10 LINES)
async function minimal() {
  const driver = await new Builder().forBrowser('chrome').build();
  try {
    await driver.get('https://example.com');
    const text = await driver.findElement(By.tagName('h1')).getText();
    console.log(text);
  } finally {
    await driver.quit();
  }
}

// Choose your template:
// googleTest();      // Most reliable
// formTest();     // For form scenarios  
ecommerceTest(); // For shopping sites
// minimal();      // Fastest to write