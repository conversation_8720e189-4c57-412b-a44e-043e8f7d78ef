import { Builder, By, WebDriver, until, WebElement, Key } from 'selenium-webdriver';
import { Options as ChromeOptions } from 'selenium-webdriver/chrome';
import { Options as FirefoxOptions } from 'selenium-webdriver/firefox';
import { Options as EdgeOptions } from 'selenium-webdriver/edge';
import { Options as SafariOptions } from 'selenium-webdriver/safari';
import * as fs from 'fs';
import * as path from 'path';

// Configuration interface for better type safety
interface TestConfig {
  browser: 'chrome' | 'firefox' | 'edge' | 'safari';
  headless: boolean;
  windowSize: { width: number; height: number };
  implicitWait: number;
  pageLoadTimeout: number;
  scriptTimeout: number;
  useBrowserStack?: boolean;
  browserStackConfig?: BrowserStackConfig;
}

// BrowserStack configuration interface
interface BrowserStackConfig {
  userName: string;
  accessKey: string;
  buildName: string;
  sessionName: string;
  os: string;
  osVersion: string;
  browserVersion: string;
  resolution?: string;
}

// Product information interface
interface ProductInfo {
  productName: string;
  displayPrice: string;
  productLink: string;
}

// BrowserStack capabilities for parallel testing
const browserStackConfigs: BrowserStackConfig[] = [
  {
    userName: 'YOUR_USERNAME', // Replace with your BrowserStack username
    accessKey: 'YOUR_ACCESS_KEY', // Replace with your BrowserStack access key
    buildName: 'Flipkart Samsung Galaxy S10 Test',
    sessionName: 'Chrome Windows 11',
    os: 'Windows',
    osVersion: '11',
    browserVersion: 'latest',
    resolution: '1920x1080'
  },
  {
    userName: 'YOUR_USERNAME',
    accessKey: 'YOUR_ACCESS_KEY',
    buildName: 'Flipkart Samsung Galaxy S10 Test',
    sessionName: 'Firefox Windows 10',
    os: 'Windows',
    osVersion: '10',
    browserVersion: 'latest',
    resolution: '1920x1080'
  },
  {
    userName: 'YOUR_USERNAME',
    accessKey: 'YOUR_ACCESS_KEY',
    buildName: 'Flipkart Samsung Galaxy S10 Test',
    sessionName: 'Chrome macOS Big Sur',
    os: 'OS X',
    osVersion: 'Big Sur',
    browserVersion: 'latest',
    resolution: '1920x1080'
  },
  {
    userName: 'YOUR_USERNAME',
    accessKey: 'YOUR_ACCESS_KEY',
    buildName: 'Flipkart Samsung Galaxy S10 Test',
    sessionName: 'Safari macOS Monterey',
    os: 'OS X',
    osVersion: 'Monterey',
    browserVersion: 'latest',
    resolution: '1920x1080'
  },
  {
    userName: 'YOUR_USERNAME',
    accessKey: 'YOUR_ACCESS_KEY',
    buildName: 'Flipkart Samsung Galaxy S10 Test',
    sessionName: 'Edge Windows 11',
    os: 'Windows',
    osVersion: '11',
    browserVersion: 'latest',
    resolution: '1920x1080'
  }
];

// Default configuration following 2025 best practices
const config: TestConfig = {
  browser: 'chrome',
  headless: false,
  windowSize: { width: 1920, height: 1080 },
  implicitWait: 10000,
  pageLoadTimeout: 30000,
  scriptTimeout: 30000
};

class FlipkartAutomation {
  private driver: WebDriver | null = null;
  private config: TestConfig;

  constructor(customConfig?: Partial<TestConfig>) {
    this.config = { ...config, ...customConfig };
  }

  async initializeDriver(): Promise<void> {
    try {
      if (this.config.useBrowserStack && this.config.browserStackConfig) {
        await this.initializeBrowserStackDriver();
      } else {
        await this.initializeLocalDriver();
      }

      // Set timeouts
      await this.driver!.manage().setTimeouts({
        implicit: this.config.implicitWait,
        pageLoad: this.config.pageLoadTimeout,
        script: this.config.scriptTimeout
      });

      console.log(`✅ ${this.config.browser} driver initialized successfully`);
    } catch (error) {
      console.error('❌ Failed to initialize driver:', error);
      throw error;
    }
  }

  private async initializeBrowserStackDriver(): Promise<void> {
    const bsConfig = this.config.browserStackConfig!;
    
    const capabilities = {
      'bstack:options': {
        userName: bsConfig.userName,
        accessKey: bsConfig.accessKey,
        buildName: bsConfig.buildName,
        sessionName: bsConfig.sessionName,
        os: bsConfig.os,
        osVersion: bsConfig.osVersion,
        resolution: bsConfig.resolution || '1920x1080',
        local: false,
        seleniumVersion: '4.0.0',
        debug: true,
        networkLogs: true
      },
      browserName: this.config.browser,
      browserVersion: bsConfig.browserVersion
    };

    this.driver = await new Builder()
      .usingServer(`https://${bsConfig.userName}:${bsConfig.accessKey}@hub-cloud.browserstack.com/wd/hub`)
      .withCapabilities(capabilities)
      .build();
  }

  private async initializeLocalDriver(): Promise<void> {
    let options: ChromeOptions | FirefoxOptions | EdgeOptions | SafariOptions;

    switch (this.config.browser) {
      case 'chrome':
        options = new ChromeOptions();
        options.addArguments('--no-sandbox');
        options.addArguments('--disable-dev-shm-usage');
        options.addArguments('--disable-gpu');
        options.addArguments(`--window-size=${this.config.windowSize.width},${this.config.windowSize.height}`);
        
        if (this.config.headless) {
          options.addArguments('--headless=new');
        }
        
        this.driver = await new Builder()
          .forBrowser('chrome')
          .setChromeOptions(options)
          .build();
        break;

      case 'firefox':
        options = new FirefoxOptions();
        if (this.config.headless) {
          options.addArguments('--headless');
        }
        
        this.driver = await new Builder()
          .forBrowser('firefox')
          .setFirefoxOptions(options)
          .build();
        break;

      case 'edge':
        options = new EdgeOptions();
        if (this.config.headless) {
          options.addArguments('--headless');
        }
        
        this.driver = await new Builder()
          .forBrowser('MicrosoftEdge')
          .setEdgeOptions(options)
          .build();
        break;

      default:
        throw new Error(`Unsupported browser: ${this.config.browser}`);
    }

    // Set window size for local browsers
    await this.driver!.manage().window().setRect({
      width: this.config.windowSize.width,
      height: this.config.windowSize.height,
      x: 0,
      y: 0
    });
  }

  async navigateToFlipkart(): Promise<void> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      console.log('🌐 Navigating to Flipkart...');
      await this.driver.get('https://www.flipkart.com');
      
      // Handle login popup if it appears
      try {
        const closeButton = await this.driver.wait(
          until.elementLocated(By.css('button._2KpZ6l._2doB4z')), 
          5000
        );
        await closeButton.click();
        console.log('✅ Closed login popup');
      } catch (error) {
        console.log('ℹ️ No login popup detected');
      }

      await this.driver.sleep(2000);
      console.log('✅ Successfully navigated to Flipkart');
    } catch (error) {
      console.error('❌ Failed to navigate to Flipkart:', error);
      throw error;
    }
  }

  async searchForProduct(productName: string): Promise<void> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      console.log(`🔍 Searching for: ${productName}`);
      
      // Find search box with multiple fallback strategies
      const searchLocators = [
        { locator: 'input[name="q"]', strategy: 'css' as const },
        { locator: 'input[placeholder="Search for products, brands and more"]', strategy: 'css' as const },
        { locator: '//input[@name="q"]', strategy: 'xpath' as const },
        { locator: 'Srch_Box', strategy: 'name' as const }
      ];

      const searchBox = await this.findElementWithFallback(searchLocators);
      
      // Clear and type search query
      await searchBox.clear();
      await this.driver.sleep(500);
      await searchBox.sendKeys(productName);
      await this.driver.sleep(1000);
      
      // Press Enter or click search button
      await searchBox.sendKeys(Key.RETURN);
      
      console.log(`✅ Search performed for: ${productName}`);
      await this.driver.sleep(3000); // Wait for search results
    } catch (error) {
      console.error('❌ Search failed:', error);
      throw error;
    }
  }

  async clickMobilesCategory(): Promise<void> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      console.log('📱 Clicking on Mobiles category...');
      
      // Multiple strategies to find Mobiles category
      const mobilesLocators = [
        { locator: '//a[contains(text(), "Mobiles")]', strategy: 'xpath' as const },
        { locator: 'a[href*="mobile"]', strategy: 'css' as const },
        { locator: '//div[contains(@class, "vertical-container")]//a[contains(text(), "Mobiles")]', strategy: 'xpath' as const },
        { locator: 'Mobiles', strategy: 'text' as const }
      ];

      const mobilesElement = await this.findElementWithFallback(mobilesLocators);
      
      // Scroll to element and click
      await this.driver.executeScript('arguments[0].scrollIntoView({block: "center"});', mobilesElement);
      await this.driver.sleep(1000);
      
      try {
        await mobilesElement.click();
      } catch (clickError) {
        // Fallback to JavaScript click
        await this.driver.executeScript('arguments[0].click();', mobilesElement);
      }
      
      console.log('✅ Successfully clicked Mobiles category');
      await this.driver.sleep(3000); // Wait for category page to load
    } catch (error) {
      console.error('❌ Failed to click Mobiles category:', error);
      throw error;
    }
  }

  async applyFilters(): Promise<void> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      console.log('🔧 Applying filters...');

      // Apply Samsung brand filter
      await this.applySamsungBrandFilter();
      
      // Apply Flipkart Assured filter
      await this.applyFlipkartAssuredFilter();
      
      // Sort by Price High to Low
      await this.sortByPriceHighToLow();
      
      console.log('✅ All filters applied successfully');
    } catch (error) {
      console.error('❌ Failed to apply filters:', error);
      throw error;
    }
  }

  private async applySamsungBrandFilter(): Promise<void> {
    try {
      console.log('🔧 Applying Samsung brand filter...');
      
      const samsungLocators = [
        { locator: '//div[contains(@class, "filter")]//div[contains(text(), "Samsung")]', strategy: 'xpath' as const },
        { locator: '//label[contains(., "Samsung")]', strategy: 'xpath' as const },
        { locator: 'input[value*="Samsung"]', strategy: 'css' as const },
        { locator: '//div[@class="_3879cV"]//div[contains(text(), "Samsung")]', strategy: 'xpath' as const }
      ];

      const samsungFilter = await this.findElementWithFallback(samsungLocators);
      await this.driver!.executeScript('arguments[0].scrollIntoView({block: "center"});', samsungFilter);
      await this.driver!.sleep(1000);
      await samsungFilter.click();
      
      console.log('✅ Samsung brand filter applied');
      await this.driver!.sleep(2000);
    } catch (error) {
      console.error('❌ Failed to apply Samsung filter:', error);
      throw error;
    }
  }

  private async applyFlipkartAssuredFilter(): Promise<void> {
    try {
      console.log('🔧 Applying Flipkart Assured filter...');
      
      const flipkartAssuredLocators = [
        { locator: '//div[contains(text(), "Flipkart Assured")]', strategy: 'xpath' as const },
        { locator: '//label[contains(., "Flipkart Assured")]', strategy: 'xpath' as const },
        { locator: 'input[value*="flipkart_assured"]', strategy: 'css' as const },
        { locator: '//div[@class="_3879cV"]//div[contains(text(), "Flipkart Assured")]', strategy: 'xpath' as const }
      ];

      const flipkartAssuredFilter = await this.findElementWithFallback(flipkartAssuredLocators);
      await this.driver!.executeScript('arguments[0].scrollIntoView({block: "center"});', flipkartAssuredFilter);
      await this.driver!.sleep(1000);
      await flipkartAssuredFilter.click();
      
      console.log('✅ Flipkart Assured filter applied');
      await this.driver!.sleep(2000);
    } catch (error) {
      console.error('❌ Failed to apply Flipkart Assured filter:', error);
      throw error;
    }
  }

  private async sortByPriceHighToLow(): Promise<void> {
    try {
      console.log('💰 Sorting by Price High to Low...');
      
      // Find and click sort dropdown
      const sortLocators = [
        { locator: '//div[contains(@class, "sort")]//select', strategy: 'xpath' as const },
        { locator: 'select[data-testid="sort"]', strategy: 'css' as const },
        { locator: '//div[contains(text(), "Sort By")]', strategy: 'xpath' as const },
        { locator: '._1k9N5V', strategy: 'css' as const }
      ];

      const sortDropdown = await this.findElementWithFallback(sortLocators);
      await sortDropdown.click();
      await this.driver!.sleep(1000);

      // Select Price High to Low option
      const priceHighToLowLocators = [
        { locator: '//div[contains(text(), "Price -- High to Low")]', strategy: 'xpath' as const },
        { locator: '//option[contains(text(), "Price -- High to Low")]', strategy: 'xpath' as const },
        { locator: 'option[value*="price_desc"]', strategy: 'css' as const }
      ];

      const priceOption = await this.findElementWithFallback(priceHighToLowLocators);
      await priceOption.click();
      
      console.log('✅ Sorted by Price High to Low');
      await this.driver!.sleep(3000); // Wait for results to reload
    } catch (error) {
      console.error('❌ Failed to sort by price:', error);
      throw error;
    }
  }

  async extractProductsFromPage1(): Promise<ProductInfo[]> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      console.log('📊 Extracting products from page 1...');
      
      // Wait for products to load
      await this.driver.sleep(3000);
      
      // Find all product containers with multiple fallback strategies
      const productLocators = [
        '//div[contains(@class, "_1AtVbE")]',
        '//div[contains(@class, "_4rR01T")]',
        '//div[contains(@class, "product")]',
        '._1AtVbE',
        '._4rR01T'
      ];

      let productElements: WebElement[] = [];
      
      for (const locator of productLocators) {
        try {
          if (locator.startsWith('//')) {
            productElements = await this.driver.findElements(By.xpath(locator));
          } else {
            productElements = await this.driver.findElements(By.css(locator));
          }
          
          if (productElements.length > 0) {
            console.log(`✅ Found ${productElements.length} products using locator: ${locator}`);
            break;
          }
        } catch (error) {
          console.log(`⚠️ Locator failed: ${locator}, trying next...`);
          continue;
        }
      }

      if (productElements.length === 0) {
        throw new Error('No product elements found on the page');
      }

      const products: ProductInfo[] = [];
      
      // Limit to first 10 products for page 1
      const maxProducts = Math.min(productElements.length, 10);
      
      for (let i = 0; i < maxProducts; i++) {
        try {
          const productElement = productElements[i];
          
          // Extract product name
          const nameElement = await this.extractProductName(productElement!);
          
          // Extract price
          const priceElement = await this.extractProductPrice(productElement!);
          
          // Extract product link
          const linkElement = await this.extractProductLink(productElement!);
          
          const product: ProductInfo = {
            productName: nameElement,
            displayPrice: priceElement,
            productLink: linkElement
          };
          
          products.push(product);
          console.log(`✅ Extracted product ${i + 1}: ${product.productName}`);
          
        } catch (error) {
          console.error(`❌ Failed to extract product ${i + 1}:`, error);
          continue; // Skip this product and continue with next
        }
      }

      console.log(`✅ Successfully extracted ${products.length} products from page 1`);
      return products;
      
    } catch (error) {
      console.error('❌ Failed to extract products:', error);
      throw error;
    }
  }

  private async extractProductName(productElement: WebElement): Promise<string> {
    const nameLocators = [
      './/div[contains(@class, "_4rR01T")]',
      './/a[contains(@class, "IRpwTa")]',
      './/div[contains(@class, "product-title")]',
      './/h3',
      './/span[contains(@class, "title")]'
    ];

    for (const locator of nameLocators) {
      try {
        const nameElement = await productElement.findElement(By.xpath(locator));
        const name = await nameElement.getText();
        if (name && name.trim()) {
          return name.trim();
        }
      } catch (error) {
        continue;
      }
    }
    return 'Name not found';
  }

  private async extractProductPrice(productElement: WebElement): Promise<string> {
    const priceLocators = [
      './/div[contains(@class, "_30jeq3")]',
      './/span[contains(@class, "price")]',
      './/div[contains(@class, "_25b18c")]',
      './/span[contains(text(), "₹")]'
    ];

    for (const locator of priceLocators) {
      try {
        const priceElement = await productElement.findElement(By.xpath(locator));
        const price = await priceElement.getText();
        if (price && price.trim() && price.includes('₹')) {
          return price.trim();
        }
      } catch (error) {
        continue;
      }
    }
    return 'Price not found';
  }

  private async extractProductLink(productElement: WebElement): Promise<string> {
    const linkLocators = [
      './/a[contains(@class, "IRpwTa")]',
      './/a[contains(@href, "/p/")]',
      './/a'
    ];

    for (const locator of linkLocators) {
      try {
        const linkElement = await productElement.findElement(By.xpath(locator));
        const href = await linkElement.getAttribute('href');
        if (href && href.includes('flipkart.com')) {
          return href;
        }
      } catch (error) {
        continue;
      }
    }
    return 'Link not found';
  }

  // All the original methods from your code
  async findElement(locator: string, strategy: 'id' | 'css' | 'xpath' | 'name' | 'text' = 'css'): Promise<WebElement> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      let element: WebElement;
      const timeout = 10000;

      switch (strategy) {
        case 'id':
          element = await this.driver.wait(until.elementLocated(By.id(locator)), timeout);
          break;
        case 'css':
          element = await this.driver.wait(until.elementLocated(By.css(locator)), timeout);
          break;
        case 'xpath':
          element = await this.driver.wait(until.elementLocated(By.xpath(locator)), timeout);
          break;
        case 'name':
          element = await this.driver.wait(until.elementLocated(By.name(locator)), timeout);
          break;
        case 'text':
          element = await this.driver.wait(until.elementLocated(By.xpath(`//*[contains(text(), '${locator}')]`)), timeout);
          break;
        default:
          throw new Error(`Unsupported locator strategy: ${strategy}`);
      }

      await this.driver.wait(until.elementIsVisible(element), timeout);
      return element;
    } catch (error) {
      console.error(`❌ Element not found: ${strategy}=${locator}`, error);
      throw error;
    }
  }

  async findElementWithFallback(locators: Array<{locator: string, strategy: 'id' | 'css' | 'xpath' | 'name' | 'text'}>): Promise<WebElement> {
    if (!this.driver) throw new Error('Driver not initialized');

    for (const {locator, strategy} of locators) {
      try {
        const element = await this.findElement(locator, strategy);
        console.log(`✅ Element found with fallback: ${strategy}=${locator}`);
        return element;
      } catch (error) {
        console.log(`⚠️ Fallback attempt failed: ${strategy}=${locator}`);
        continue;
      }
    }
    
    throw new Error(`All fallback strategies failed for locators: ${JSON.stringify(locators)}`);
  }

  async takeScreenshot(filename?: string): Promise<string> {
    if (!this.driver) throw new Error('Driver not initialized');

    try {
      const screenshot = await this.driver.takeScreenshot();
      const screenshotPath = filename || `flipkart_screenshot_${Date.now()}.png`;
      
      const screenshotsDir = path.join(process.cwd(), 'screenshots');
      if (!fs.existsSync(screenshotsDir)) {
        fs.mkdirSync(screenshotsDir, { recursive: true });
      }
      
      const fullPath = path.join(screenshotsDir, screenshotPath);
      fs.writeFileSync(fullPath, screenshot, 'base64');
      
      console.log(`📸 Screenshot saved: ${fullPath}`);
      return fullPath;
    } catch (error) {
      console.error('❌ Screenshot failed:', error);
      throw error;
    }
  }

  async saveDataToFile(data: any, filename: string): Promise<void> {
    try {
      const dataDir = path.join(process.cwd(), 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }
      
      const fullPath = path.join(dataDir, filename);
      fs.writeFileSync(fullPath, JSON.stringify(data, null, 2));
      console.log(`💾 Data saved to: ${fullPath}`);
    } catch (error) {
      console.error('❌ File save failed:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    if (this.driver) {
      try {
        await this.driver.quit();
        console.log('🧹 Driver cleanup completed');
      } catch (error) {
        console.error('❌ Cleanup failed:', error);
      }
    }
  }

  // Main test execution method
  async runFlipkartTest(): Promise<ProductInfo[]> {
    try {
      await this.initializeDriver();
      await this.navigateToFlipkart();
      await this.searchForProduct('Samsung Galaxy S10');
      await this.clickMobilesCategory();
      await this.applyFilters();
      
      const products = await this.extractProductsFromPage1();
      
      // Print results to console
      console.log('\n🎯 FINAL RESULTS - Samsung Galaxy S10 Products (Page 1):');
      console.log('='.repeat(80));
      
      products.forEach((product, index) => {
        console.log(`\n📱 Product ${index + 1}:`);
        console.log(`   Name: ${product.productName}`);
        console.log(`   Price: ${product.displayPrice}`);
        console.log(`   Link: ${product.productLink}`);
      });

      // Save to file
      await this.saveDataToFile({
        testInfo: {
          browser: this.config.browser,
          browserStack: this.config.useBrowserStack,
          timestamp: new Date().toISOString(),
          searchQuery: 'Samsung Galaxy S10',
          totalProducts: products.length
        },
        products: products
      }, `flipkart_products_${this.config.browser}_${Date.now()}.json`);

      await this.takeScreenshot(`final_results_${this.config.browser}.png`);
      
      return products;
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      await this.takeScreenshot(`error_${this.config.browser}_${Date.now()}.png`);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// Function to run single test
async function runSingleTest(browserStackConfig?: BrowserStackConfig): Promise<ProductInfo[]> {
  const testConfig: TestConfig = {
    browser: browserStackConfig?.sessionName.toLowerCase().includes('chrome') ? 'chrome' :
             browserStackConfig?.sessionName.toLowerCase().includes('firefox') ? 'firefox' :
             browserStackConfig?.sessionName.toLowerCase().includes('edge') ? 'edge' :
             browserStackConfig?.sessionName.toLowerCase().includes('safari') ? 'safari' : 'chrome',
    headless: false,
    windowSize: { width: 1920, height: 1080 },
    implicitWait: 15000,
    pageLoadTimeout: 45000,
    scriptTimeout: 30000,
    useBrowserStack: !!browserStackConfig,
    browserStackConfig: browserStackConfig
  };

  const automation = new FlipkartAutomation(testConfig);
  return await automation.runFlipkartTest();
}

// Function to run parallel tests on BrowserStack
async function runParallelBrowserStackTests(): Promise<void> {
  console.log('🚀 Starting parallel BrowserStack tests...');
  console.log('⚠️ Make sure to replace YOUR_USERNAME and YOUR_ACCESS_KEY with your actual BrowserStack credentials');
  
  const promises = browserStackConfigs.map(async (config, index) => {
    try {
      console.log(`\n🎯 Starting test ${index + 1}: ${config.sessionName}`);
      const products = await runSingleTest(config);
      console.log(`✅ Test ${index + 1} completed successfully with ${products.length} products`);
      return { config: config.sessionName, products, success: true };
    } catch (error) {
      console.error(`❌ Test ${index + 1} failed:`, error);
      return { config: config.sessionName, products: [], success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

  const results = await Promise.allSettled(promises);
  
  // Summary report
  console.log('\n📋 PARALLEL TEST EXECUTION SUMMARY:');
  console.log('='.repeat(60));
  
  results.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const testResult = result.value;
      console.log(`\n${testResult.success ? '✅' : '❌'} ${browserStackConfigs[index]?.sessionName}:`);
      if (testResult.success) {
        console.log(`   Products found: ${testResult.products.length}`);
      } else {
        console.log(`   Error: ${testResult.error}`);
      }
    } else {
    }
  });

  // Save consolidated results
  const consolidatedResults = results.map((result, index) => ({
    browserConfig: browserStackConfigs[index],
    success: result.status === 'fulfilled' && result.value.success,
    products: result.status === 'fulfilled' ? result.value.products : [],
    error: result.status === 'fulfilled' ? result.value.error : result.reason
  }));

  const automation = new FlipkartAutomation();
  await automation.saveDataToFile({
    testSuite: 'BrowserStack Parallel Testing',
    timestamp: new Date().toISOString(),
    totalConfigurations: browserStackConfigs.length,
    successfulTests: consolidatedResults.filter(r => r.success).length,
    results: consolidatedResults
  }, `browserstack_parallel_results_${Date.now()}.json`);

  console.log('\n🎉 Parallel testing completed!');
}

// Function to run local test (without BrowserStack)
async function runLocalTest(): Promise<ProductInfo[]> {
  console.log('🏠 Running local test...');
  return await runSingleTest();
}

// Main execution function
async function main() {
  console.log('🎯 Flipkart Samsung Galaxy S10 Automation Test');
  console.log('='.repeat(50));
  
  // Get command line arguments
  const args = process.argv.slice(2);
  const runMode = args[0] || 'local'; // 'local' or 'browserstack'
  
  try {
    if (runMode === 'browserstack') {
      console.log('🌐 Running tests on BrowserStack...');
      await runParallelBrowserStackTests();
    } else {
      console.log('🏠 Running local test...');
      const products = await runLocalTest();
      
      // Display results
      console.log('\n🎯 FINAL RESULTS - Samsung Galaxy S10 Products:');
        console.log('='.repeat(70));
      
      if (products.length === 0) {
        console.log('❌ No products found');
      } else {
        products.forEach((product, index) => {
          console.log(`\n📱 Product ${index + 1}:`);
          console.log(`   Product Name: ${product.productName}`);
          console.log(`   Display Price: ${product.displayPrice}`);
          console.log(`   Product Link: ${product.productLink}`);
        });
        
        console.log(`\n✅ Total products extracted: ${products.length}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Setup instructions and configuration helper
function printSetupInstructions(): void {
  console.log(`
🔧 SETUP INSTRUCTIONS:
=====================

1. Install Dependencies:
   npm install selenium-webdriver
   npm install @types/selenium-webdriver (for TypeScript)

2. Install Browser Drivers:
   - Chrome: Download ChromeDriver from https://chromedriver.chromium.org/
   - Firefox: Download GeckoDriver from https://github.com/mozilla/geckodriver/releases
   - Edge: Download EdgeDriver from https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/

3. BrowserStack Setup:
   - Create free trial account at https://www.browserstack.com/
   - Get your Username and Access Key from Account Settings
   - Replace 'YOUR_USERNAME' and 'YOUR_ACCESS_KEY' in browserStackConfigs array

4. Run Tests:
   Local test:        npm run test (or node dist/index.js local)
   BrowserStack test: npm run test:browserstack (or node dist/index.js browserstack)

📋 ASSIGNMENT REQUIREMENTS COVERED:
==================================
✅ BrowserStack integration with 5 parallel configurations
✅ Navigate to flipkart.com desktop homepage  
✅ Search for "Samsung Galaxy S10"
✅ Click on "Mobiles" category
✅ Apply Samsung brand filter
✅ Apply Flipkart Assured filter  
✅ Sort by Price High to Low
✅ Extract page 1 results with:
   - Product Name
   - Display Price  
   - Product Details Link
✅ Console output of results
✅ Parallel execution on different browser/OS combinations
✅ Error handling and robust element finding
✅ Screenshot and data file generation

🎯 TEST CONFIGURATIONS:
======================
1. Chrome on Windows 11
2. Firefox on Windows 10  
3. Chrome on macOS Big Sur
4. Safari on macOS Monterey
5. Edge on Windows 11
  `);
}

// Export for use as module
export { 
  FlipkartAutomation, 
  type TestConfig, 
  type BrowserStackConfig, 
  type ProductInfo,
  runSingleTest,
  runParallelBrowserStackTests,
  runLocalTest,
  printSetupInstructions
};

// Run if called directly
if (import.meta.main) {
  // Print setup instructions first
  printSetupInstructions();
  
  // Then run the main function
  main().catch(console.error);
}