import { Builder, By, Key, until } from 'selenium-webdriver';
import { Options } from 'selenium-webdriver/chrome';

const options = new Options();
options.addArguments('--no-sandbox');
options.addArguments('--disable-dev-shm-usage');

const driver = await new Builder()
.forBrowser('chrome')
.setChromeOptions(options)
.build();



async function seleniumTest() {
  try {
    // Navigate to website
    await driver.get('https://example.com');
    await driver.sleep(2000);

    // Extract text
    const title = await driver.findElement(By.css('h1')).getText();
    const description = await driver.findElement(By.css('p')).getText();
    console.log('Title:', title);
    console.log('Description:', description);

    // Form interaction
    await driver.get('https://httpbin.org/forms/post');
    await driver.findElement(By.name('custname')).sendKeys('<PERSON>');
    await driver.findElement(By.name('custemail')).sendKeys('<EMAIL>');
    await driver.findElement(By.name('topping')).click();

    // Search functionality
    await driver.get('https://google.com');
    const search = await driver.wait(until.elementLocated(By.name('q')), 10000);
    await search.sendKeys('selenium automation');
    await search.sendKeys(Key.RETURN);
    await driver.sleep(3000);

    // Scroll page
    await driver.executeScript('window.scrollTo(0, document.body.scrollHeight);');
    await driver.sleep(1000);

    // Extract multiple elements
    const links = await driver.findElements(By.css('a'));
    console.log(`Found ${links.length} links`);

    // Take screenshot
    const screenshot = await driver.takeScreenshot();
    console.log('Screenshot taken');

    // Get page info
    const pageTitle = await driver.getTitle();
    const currentUrl = await driver.getCurrentUrl();
    console.log('Page Title:', pageTitle);
    console.log('Current URL:', currentUrl);

  } finally {
    await driver.quit();
  }
}

seleniumTest();