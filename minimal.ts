import { Builder, By, Key, until, WebDriver } from 'selenium-webdriver';
import { Options } from 'selenium-webdriver/chrome';

async function createDriver(): Promise<WebDriver> {
  const options = new Options();
  options.addArguments('--no-sandbox');
  options.addArguments('--disable-dev-shm-usage');

  return await new Builder()
    .forBrowser('chrome')
    .setChromeOptions(options)
    .build();
}

async function testWebsite(driver: WebDriver) {
  await driver.get('https://example.com');
  await driver.sleep(2000);
  
  const title = await driver.findElement(By.css('h1')).getText();
  console.log('Title:', title);
}

async function testForm(driver: WebDriver) {
  await driver.get('https://httpbin.org/forms/post');
  await driver.findElement(By.name('custname')).sendKeys('<PERSON>');
  await driver.findElement(By.name('custemail')).sendKeys('<EMAIL>');
}

async function testSearch(driver: WebDriver) {
  await driver.get('https://google.com');
  const search = await driver.wait(until.elementLocated(By.name('q')), 10000);
  await search.sendKeys('selenium automation');
  await search.sendKeys(Key.RETURN);
}

async function runAllTests() {
  const driver = await createDriver();
  
  try {
    await testWebsite(driver);
    await testForm(driver);
    await testSearch(driver);
  } finally {
    await driver.quit(); // Only quit once, after all tests
  }
}

runAllTests();